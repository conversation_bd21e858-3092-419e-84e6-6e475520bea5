package cn.gf.saas.framework.mybatis.core.type;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.MD5;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 字段字段的 TypeHandler 实现类，基于 {@link cn.hutool.crypto.digest.MD5} 实现
 * 可通过 jasypt.encryptor.password 配置项，设置密钥
 *
 * <AUTHOR>
 */
public class MD5EncryptTypeHandler extends BaseTypeHandler<String> {

    private static MD5 md5;

    public static String encrypt(String rawValue) {
        if (rawValue == null) {
            return null;
        }
        return getEncryptor().digestHex(rawValue);
    }

    private static MD5 getEncryptor() {
        if (md5 != null) {
            return md5;
        }
        // 构建 md5
        md5 = SecureUtil.md5();
        return md5;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, encrypt(parameter));
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return rs.getString(columnName);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return rs.getString(columnIndex);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return cs.getString(columnIndex);
    }
}
