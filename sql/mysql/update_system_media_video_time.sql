-- 修改system_media表的video_time字段类型
-- 将video_time字段从VARCHAR类型改为DECIMAL(10,2)类型，用于存储视频时长（单位：秒，保留两位小数）

-- 备份现有数据（可选）
-- CREATE TABLE system_media_backup AS SELECT * FROM system_media WHERE video_time IS NOT NULL;

-- 修改字段类型
ALTER TABLE system_media 
MODIFY COLUMN video_time DECIMAL(10,2) COMMENT '视频时长（单位：秒，保留两位小数）';

-- 如果需要更新现有数据，可以使用以下SQL（根据实际数据格式调整）
-- 假设原来的video_time是字符串格式，如 "00:01:30" 或 "90" 等
-- UPDATE system_media 
-- SET video_time = CASE 
--     WHEN video_time REGEXP '^[0-9]+:[0-9]+:[0-9]+$' THEN 
--         TIME_TO_SEC(STR_TO_DATE(video_time, '%H:%i:%s'))
--     WHEN video_time REGEXP '^[0-9]+\\.?[0-9]*$' THEN 
--         CAST(video_time AS DECIMAL(10,2))
--     ELSE NULL
-- END
-- WHERE video_time IS NOT NULL AND video_time != '';
