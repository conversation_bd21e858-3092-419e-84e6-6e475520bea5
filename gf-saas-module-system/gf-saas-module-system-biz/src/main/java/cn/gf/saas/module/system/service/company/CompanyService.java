package cn.gf.saas.module.system.service.company;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.module.system.api.company.dto.AppAuthCompanyDepartmentRespDTO;
import cn.gf.saas.module.system.api.company.dto.CompanyReqDTO;
import cn.gf.saas.module.system.api.company.dto.CompanyRespDTO;
import cn.gf.saas.module.system.controller.admin.company.vo.*;
import cn.gf.saas.module.system.dal.dataobject.user.AdminUserDO;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface CompanyService {

    List<CompanySimpleRespVO> getCompanyListByIds(Set<String> companyIds);

    List<CompanyRespDTO> getCompanyListByIdsAndSource(Set<String> companyIds, String source);

    PageResult<CompanyRespDTO> getCompanyPageByIdsAndSource(CompanyReqDTO companyReqDTO);

    List<String> getAllParentByCompanyId(String companyId);

    Map<Long, List<String>> getCompanyIdsByMenuId(Set<Long> menuIds);

    void updateCompanyMenuById(String companyId, Long menuId);

    List<CompanySimpleRespVO> getCompanyList(CompanyListReqVO reqVO);

    PageResult<CompanyRespVO> getCompanyPage(CompanyListReqVO reqVO);

    CompanyRespVO getCompany(String companyId);

    String createCompany(CompanyReqVO companyReqVO);

    void updateCompany(CompanyUpdateReqVO companyReqVO);

    void updateCompanyUser(CompanyUserUpdateReqVO companyReqVO);

    void updateCompanyPackage(CompanyPackageReqVO companyReqVO);

    Long getCompanyLeaderRoleIdCompanyIdUserId(Long userId, String companyId);

    List<CompanySimpleRespVO> getCurrentUserCompanyTree(Long userId, String parentId, String targetCompanyId, String source);

    Boolean validCompany(String companyId);

    List<CompanyRespVO> exportCompanyExcel(CompanyListReqVO reqVO);

    /**
     * 获取公司部门信息
     *
     * @param companyId 单位id
     */
    AppAuthCompanyDepartmentRespDTO getCompanyDepartment(String companyId);

    /**
     * 获取公司部门信息
     *
     * @return  CompanyPackageRespVO 单位套餐信息
     */
    CompanyPackageRespVO getCompanyPackage(String companyId);
}
