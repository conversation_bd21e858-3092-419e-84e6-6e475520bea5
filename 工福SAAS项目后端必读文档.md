# 工福SAAS项目后端技术文档

# 快速启动

## 1. 克隆代码

使用[IDEA](http://www.iocoder.cn/categories/IDEA/?self)克隆 \*\* 仓库的最新代码

克隆完成后，耐心等待 Maven 下载完相关的依赖。注意：

默认情况下，使用`master`分支，它对应 JDK 8 + Spring Boot 2.7 版本。

## 2.基础设施

### 1.初始化Mysql

项目使用 MySQL 存储数据，所以需要启动一个 MySQL 服务。

① 创建一个名字为 saas 数据库，**【只要】**执行对应数据库类型的[
`sql`](https://github.com/YunaiV/yudao-cloud/tree/master/sql)目录下的 \*\*.sql SQL 文件，进行初始化。

② 默认配置下，MySQL 需要启动在 3306 端口，并且账号是 \*\*，密码是 \*\*。如果不一致，需要修改`application-local.yaml`配置文件。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/8a527e28-8ecd-43ba-9403-67446ad9fa5d.png)

### 2.初始化 Redis

默认配置下，Redis 启动在 6379 端口，不设置账号密码。如果不一致，需要修改`application-local.yaml`配置文件

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/aabf0dac-f60f-4296-92c8-681f82b7a884.png)

### 3.初始化 Nacos

项目使用 Nacos 作为**注册中心**和**配置中心**
，参考[《工福 Nacos 极简入门》](http://www.iocoder.cn/Nacos/install/?yudao-cloud)
文章，进行安装，只需要看该文的[「2. 单机部署（最简模式）」](https://cloud.iocoder.cn/quick-start/#)即可。

安装完成之后，需要创建`dev`**命名空间**，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/8fc6767a-e509-400e-8f34-b6e4b3ee04ae.png)

### 4.编译项目

第一步，使用 IDEA 自带的 Maven 插件，进行项目的编译。如下图所示

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/c9b0d4bb-4d60-4faa-9012-c832514f561e.png)

**【可选】也可以使用 Maven 命令编译：**

* 使用 IDEA 打开 Terminal 终端，在**根目录**下直接执行`mvn clean install package '-Dmaven.test.skip=true'`命令。

* 如果执行报`Unknown lifecycle phase “.test.skip=true”`错误，使用`mvn clean install package -Dmaven.test.skip=true`即可。

ps：只有首次需要执行 Maven 命令，解决基础`pom.xml`不存在，导致报 BaseDbUnitTest 类不存在的问题。

**5.启动**`**gateway**`**服务**

执行[GatewayServerApplication (opens new window)](https://github.com/YunaiV/yudao-cloud/blob/master/yudao-gateway/src/main/java/cn/iocoder/yudao/gateway/GatewayServerApplication.java)
类，进行启动。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/9ac46639-caf6-47cb-b4ff-1829a5b147dc.png)

启动完成后，使用浏览器访问[http://127.0.0.1:48080](http://127.0.0.1:48080/)地址，返回如下 JSON 字符串，说明成功

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/4b7becec-b37a-47c7-90e1-1c8cd23fddfd.png)

**6.启动**`**system**`**服务**

执行[SystemServerApplication](https://github.com/YunaiV/yudao-cloud/blob/master/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/SystemServerApplication.java)
类，进行启动。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/f4306c0d-c4ae-4432-81c9-5b222df59e45.png)

启动完成后，使用浏览器访问[http://127.0.0.1:48081/admin-api/system/ (opens new window)](http://127.0.0.1:48081/admin-api/system/)
和[http://127.0.0.1:48080/admin-api/system/ (opens new window)](http://127.0.0.1:48080/admin-api/system/)地址，都返回如下
JSON 字符串，说明成功。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/9ddc935a-ecec-48fe-93ae-ddb58614e3f5.png)

### 7.启动 `**infra**` 服务

执行[InfraServerApplication (opens new window)](https://github.com/YunaiV/yudao-cloud/blob/master/yudao-module-infra/yudao-module-infra-biz/src/main/java/cn/iocoder/yudao/module/infra/InfraServerApplication.java)
类，进行启动。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/67894baf-5e61-44bd-8596-11a166ed17e1.png)

启动完成后，使用浏览器访问[http://127.0.0.1:48082/admin-api/infra/ (opens new window)](http://127.0.0.1:48082/admin-api/infra/)
和[http://127.0.0.1:48080/admin-api/infra/ (opens new window)](http://127.0.0.1:48080/admin-api/infra/)地址，都返回如下
JSON 字符串，说明成功。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/d9d272b0-72eb-4a83-88c7-19bb004cdd84.png)

## 新建服务

### 1. 新建 demo 模块

① 选择 File ->New ->Module 菜单，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/6842e8e7-1942-400d-8d9e-095025d604b2.png)

② 选择 Maven 类型，选择父模块为`gf-saas`，输入名字为`gf-module-demo`，并点击 Create 按钮，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/1189a7a4-f062-435f-9b1c-d18295f5b616.png)

③ 打开`gf-module-demo`模块，删除 src 文件，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/8073de6d-4de7-44c7-aea2-42c746097554.png)

④ 打开`yudao-module-demo`模块的`pom.xml`文件，修改内容如下：

**提示**

`<!-- -->`部分，只是注释，不需要写到 XML 中。

```xml

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-saas</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>gf-module-demo</artifactId>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description> <!--  新增 description 为该模块的描述 -->
        demo 模块，主要实现 XXX、YYY、ZZZ 等功能。
    </description>
</project>

```

## [**#

**](https://cloud.iocoder.cn/module-new/#_2-%E6%96%B0%E5%BB%BA-demo-api-%E5%AD%90%E6%A8%A1%E5%9D%97)2. 新建 demo-api 子模块

① 新建`yudao-module-demo-api`子模块，整个过程和“新建 demo 模块”是一致的，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/ffc5c5c1-c233-4d57-8348-ca1c7dda4289.png)

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/21e25c85-07f6-4d22-93d1-2f7ce8ac4c7d.png)

② 打开`yudao-module-demo-api`模块的`pom.xml`文件，修改内容如下：

```xml

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-module-demo</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>gf-module-demo-api</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description> <!--  新增 description 为该模块的描述 -->
        demo 模块 API，暴露给其它模块调用
    </description>

    <dependencies>  <!-- 新增 gf-saas-common 依赖 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-common</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>
</project>

```

③ 【可选】新建`cn.gf.saas.module.infra`**基础**包，其中`demo`为模块名。之后，新建`api`和`enums`包。如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/69b6b2e7-455b-4a7e-906e-d5d21fcca738.png)

## [**#

**](https://cloud.iocoder.cn/module-new/#_3-%E6%96%B0%E5%BB%BA-demo-biz-%E5%AD%90%E6%A8%A1%E5%9D%97)3. 新建 demo-biz 子模块

① 新建`gf-module-demo-biz`子模块，整个过程和“新建 demo 模块”也是一致的，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/329ca6f1-09d7-4e41-b273-aaa107a8698a.png)

② 打开`gf-module-demo-biz`模块的`pom.xml`文件，修改成内容如下：

```xml

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.gf.saas</groupId>
        <artifactId>gf-module-demo</artifactId>
        <version>${revision}</version>  <!-- 1. 修改 version 为 ${revision} -->
    </parent>
    <artifactId>gf-module-demo-biz</artifactId>
    <packaging>jar</packaging>  <!-- 2. 新增 packaging 为 jar -->
    <name>${project.artifactId}</name> <!-- 3. 新增 name 为 ${project.artifactId} -->
    <description> <!-- 4. 新增 description 为该模块的描述 -->
        demo 模块，主要实现 XXX、YYY、ZZZ 等功能。
    </description>

    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-env</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-module-demo-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-web</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-excel</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.gf.saas</groupId>
            <artifactId>gf-saas-monitor</artifactId>
        </dependency>
    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <configuration>
                    <fork>true</fork>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

```

③ 【必选】新建`cn.gf.saas.module.app`**基础**包，其中`demo`为模块名。之后，新建`controller.admin`和`controller.user`
等包。如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/47319d9a-97e1-4d66-af56-1479ec995f7e.png)

【JDK 8 + Spring Boot 2.X 版本】其中 SecurityConfiguration 的 Java 代码如下：

```java
package cn.gf.saas.module.app.framework.security.config;

import cn.gf.saas.framework.security.config.AuthorizeRequestsCustomizer;
import enums.cn.gf.saas.module.app.ApiConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;

/**
 * Infra 模块的 Security 配置
 */
@Configuration(proxyBeanMethods = false, value = "infraSecurityConfiguration")
public class SecurityConfiguration {

    @Value("${spring.boot.admin.context-path:''}")
    private String adminSeverContextPath;

    @Bean("infraAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {

            @Override
            public void customize(ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry) {
                // Swagger 接口文档
                registry.antMatchers("/v3/api-docs/**").permitAll()
                        .antMatchers("/webjars/**").permitAll()
                        .antMatchers("/swagger-ui").permitAll()
                        .antMatchers("/swagger-ui/**").permitAll();
                // Spring Boot Actuator 的安全配置
                registry.antMatchers("/actuator").anonymous()
                        .antMatchers("/actuator/**").anonymous();
                // Druid 监控
                registry.antMatchers("/druid/**").anonymous();
                // Spring Boot Admin Server 的安全配置
                registry.antMatchers(adminSeverContextPath).anonymous()
                        .antMatchers(adminSeverContextPath + "/**").anonymous();
                // 文件读取
                registry.antMatchers(buildAdminApi("/infra/file/*/get/**")).permitAll();

                // TODO：这个每个项目都需要重复配置，得捉摸有没通用的方案
                // RPC 服务的安全配置
                registry.antMatchers(ApiConstants.PREFIX + "/**").permitAll();
            }

        };
    }

}

```

其中 DemoServerApplication 的 Java 代码如下：

```java
package cn.gf.saas.module.app;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 项目的启动类
 *
 * <AUTHOR>
@SpringBootApplication
public class DemoServerApplication {

    public static void main(String[] args) {

        SpringApplication.run(DemoServerApplication.class, args);
    }

}

```

④ 打开 Maven 菜单，点击刷新按钮，让引入的 Maven 依赖生效。如下图所示：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/2b72461d-9bd2-436b-921f-9e90557bbac8.png)

⑤ 在`resources`目录下，新建配置文件。如下图所示：

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/aa738af0-8ade-4835-a8be-6388a5fcc6c1.png)

其中`application.yml`的配置如下：

```yaml
spring:
  application:
    name: demo-server

  profiles:
    active: local

  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务

  config:
    import:
      - optional:classpath:application-${spring.profiles.active}.yaml # 加载【本地】配置
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml # 加载【Nacos】的配置

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

server:
  port: 48099

logging:
  file:
    name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${gf.info.base-package}.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 关闭控制台的 Banner 打印

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口

--- #################### RPC 远程调用相关配置 ####################

--- #################### 消息队列相关 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  # Producer 配置项
  producer:
    group: ${spring.application.name}_PRODUCER # 生产者分组

spring:
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    # Kafka Producer 配置项
    producer:
      acks: 1 # 0-不应答。1-leader 应答。all-所有 leader 和 follower 应答。
      retries: 3 # 发送失败时，重试发送的次数
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer # 消息的 value 的序列化
    # Kafka Consumer 配置项
    consumer:
      auto-offset-reset: earliest # 设置消费者分组最初的消费进度为 earliest 。可参考博客 https://blog.csdn.net/lishuangzhe7047/article/details/74530417 理解
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: '*'
    # Kafka Consumer Listener 监听器配置
    listener:
      missing-topics-fatal: false # 消费监听接口监听的主题不存在时，默认会报错。所以通过设置为 false ，解决报错

--- #################### 定时任务相关配置 ####################

xxl:
  job:
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.home}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
    accessToken: default_token # 执行器通讯TOKEN

--- #################### 工福相关配置 ####################

gf:
  info:
    version: 1.0.0
    base-package: cn.gf.saas.module.app
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  websocket:
    enable: true # websocket的开关
    path: /demo/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${gf.info.version}
  codegen:
    base-package: cn.gf.saas.module
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多租户相关配置项
    enable: true
    ignore-urls:
      - /admin-api/demo/file/*/get/** # 获取图片，和租户无关
    ignore-tables:
      - demo_codegen_column
      - demo_codegen_table
      - demo_config
      - demo_file_config
      - demo_file
      - demo_file_content
      - demo_job
      - demo_job_log
      - demo_job_log
      - demo_data_source_config

debug: false

```

* `spring.application.name`配置项：可以改成你想要的服务名。

* `server.port`配置项：可以改成你想要的端口号。

* `yudao.info.version.base-package`配置项：可以改成你的项目的基准包名。

其中`application-local.yml`的配置如下：

```yaml
--- #################### 注册中心 + 配置中心相关配置 ####################

spring:
  cloud:
    nacos:
      server-addr: http://mse-42ab81b16-p.nacos-ans.mse.aliyuncs.com:8848 # Nacos 服务器地址
      #      username: # Nacos 账号
      #      password: # Nacos 密码
      discovery: # 【配置中心】配置项
        namespace: fafb5c7a-e254-477c-ad94-e7c3d7172fc1 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        access-key: LTAI5tH7xCLUrsNLaUJvAGUo
        secret-key: ******************************
        metadata:
          version: 1.0.0 # 服务实例的版本号，可用于灰度发布
      config: # 【注册中心】配置项
        namespace: fafb5c7a-e254-477c-ad94-e7c3d7172fc1 # 命名空间。这里使用 dev 开发环境
        group: DEFAULT_GROUP # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP

--- #################### 数据库相关配置 ####################
spring:

  # 数据源配置项
  autoconfigure:
    exclude:
  #      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  #      - de.codecentric.boot.admin.server.config.AdminServerAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
  #      - de.codecentric.boot.admin.server.cloud.config.AdminServerDiscoveryAutoConfiguration # 禁用 Spring Boot Admin 的 Server 的自动配置
  #      - de.codecentric.boot.admin.server.ui.config.AdminServerUiAutoConfiguration # 禁用 Spring Boot Admin 的 Server UI 的自动配置
  #      - de.codecentric.boot.admin.client.config.SpringBootAdminClientAutoConfiguration # 禁用 Spring Boot Admin 的 Client 的自动配置
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 1 # 初始连接数
        min-idle: 1 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        master:
          url: ************************************************************************************************************************************************ # MySQL Connector/J 8.X 连接的示例
          #          url: ******************************************************************************************************************************************************* # MySQL Connector/J 5.X 连接的示例
          #          url: ********************************************** # PostgreSQL 连接的示例
          #          url: *********************************** # Oracle 连接的示例
          #          url: ********************************************************** # SQLServer 连接的示例
          #          url: jdbc:dm://10.211.55.4:5236?schema=RUOYI_VUE_PRO # DM 连接的示例
          username: meta
          password: M@ta2015
        #          username: sa # SQL Server 连接的示例
        #          password: JSm:g(*%lU4ZAkz06cd52KqT3)i1?H7W # SQL Server 连接的示例
        #          username: SYSDBA # DM 连接的示例
        #          password: SYSDBA # DM 连接的示例
  #        slave: # 模拟从库，可根据自己需要修改
  #          lazy: true # 开启懒加载，保证启动速度
  #          url: ************************************************************************************************************************************************
  #          username: meta
  #          password: M@ta2015

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    host: dbtest.goodfull.vip # 地址
    port: 6543 # 端口
    database: 14 # 数据库索引
    password: M@ta2015
#    password: 123456 # 密码，建议生产环境开启

--- #################### MQ 消息队列相关配置 ####################

# rocketmq 配置项，对应 RocketMQProperties 配置类
rocketmq:
  name-server: 127.0.0.1:9876 # RocketMQ Namesrv

spring:
  # RabbitMQ 配置项，对应 RabbitProperties 配置类
  rabbitmq:
    host: 127.0.0.1 # RabbitMQ 服务的地址
    port: 5672 # RabbitMQ 服务的端口
    username: guest # RabbitMQ 服务的账号
    password: guest # RabbitMQ 服务的密码
  # Kafka 配置项，对应 KafkaProperties 配置类
  kafka:
    bootstrap-servers: 127.0.0.1:9092 # 指定 Kafka Broker 地址，可以设置多个，以逗号分隔

--- #################### 定时任务相关配置 ####################
xxl:
  job:
    enabled: false # 是否开启调度中心，默认为 true 开启
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

# 日志文件配置
logging:
  level:
    # 配置自己写的 MyBatis Mapper 打印日志
    cn.gf.saas.module.infra.dal.mysql: debug
    cn.gf.saas.module.infra.dal.mysql.logger.ApiErrorLogMapper: INFO # 配置 ApiErrorLogMapper 的日志级别为 info，避免和 GlobalExceptionHandler 重复打印
    cn.gf.saas.module.infra.dal.mysql.file.FileConfigMapper: INFO # 配置 FileConfigMapper 的日志级别为 info
    org.springframework.context.support.PostProcessorRegistrationDelegate: ERROR # TODO 开发：先禁用，Spring Boot 3.X 存在部分错误的 WARN 提示

--- #################### 工福相关配置 ####################

# 工福配置项，设置当前项目所有自定义的配置
gf:
  env: # 多环境的配置项
    tag: ${HOSTNAME}
  security:
    mock-enable: true
  access-log: # 访问日志的配置项
    enable: false
```

* `logging.level.cn.gf.saas.module.demo.dal.mysql`配置项：可以改成你的项目的基准包名。

其中`logback-spring.xml`的配置如下：

```xml

<configuration>
    <!-- 引用 Spring Boot 的 logback 基础配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!-- 变量 gf.info.base-package，基础业务包 -->
    <springProperty scope="context" name="gf.info.base-package" source="gf.info.base-package"/>
    <!-- 格式化输出：%d 表示日期，%X{tid} SkWalking 链路追踪编号，%thread 表示线程名，%-5level：级别从左显示 5 个字符宽度，%msg：日志消息，%n是换行符 -->
    <property name="PATTERN_DEFAULT"
              value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} | %highlight(${LOG_LEVEL_PATTERN:-%5p} ${PID:- }) | %boldYellow(%thread [%tid]) %boldGreen(%-40.40logger{39}) | %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- 控制台 Appender -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">　　　　　
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 文件 Appender -->
    <!-- 参考 Spring Boot 的 file-appender.xml 编写 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
        <!-- 日志文件名 -->
        <file>${LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 滚动后的日志文件名 -->
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz}
            </fileNamePattern>
            <!-- 启动服务时，是否清理历史日志，一般不建议清理 -->
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <!-- 日志文件，到达多少容量，进行滚动 -->
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-10MB}</maxFileSize>
            <!-- 日志文件的总大小，0 表示不限制 -->
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <!-- 日志文件的保留天数 -->
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
    </appender>
    <!-- 异步写入日志，提升性能 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志。默认的，如果队列的 80% 已满,则会丢弃 TRACT、DEBUG、INFO 级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度，该值会影响性能。默认值为 256 -->
        <queueSize>256</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- SkyWalking GRPC 日志收集，实现日志中心。注意：SkyWalking 8.4.0 版本开始支持 -->
    <appender name="GRPC" class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.log.GRPCLogClientAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>${PATTERN_DEFAULT}</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- 本地环境 -->
    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="GRPC"/> <!-- 本地环境下，如果不想接入 SkyWalking 日志服务，可以注释掉本行 -->
            <appender-ref ref="ASYNC"/>  <!-- 本地环境下，如果不想打印日志，可以注释掉本行 -->
        </root>
    </springProfile>
    <!-- 其它环境 -->
    <springProfile name="dev,test,stage,prod,default">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ASYNC"/>
            <appender-ref ref="GRPC"/>
        </root>
    </springProfile>

</configuration>

```

## [**#

**](https://cloud.iocoder.cn/module-new/#_4-%E6%96%B0%E5%BB%BA-restful-api-%E6%8E%A5%E5%8F%A3)4. 新建 RESTful API 接口

① 在`controller.admin`包，新建一个 DemoTestController 类，并新建一个`/demo/test/get`接口。代码如下：

```java
package cn.gf.saas.module.app.controller.admin;

import cn.gf.saas.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - Test")
@RestController
@RequestMapping("/demo/test")
@Validated
public class DemoTestController {

    @GetMapping("/get")
    @Operation(summary = "获取 test 信息")
    public CommonResult<String> get() {
        return success("true");
    }

}

```

**注意**，`/demo`是该模块所有 RESTful API 的基础路径，`/test`是 Test 功能的基础路径。

① 在`controller.app`包，新建一个 AppDemoTestController 类，并新建一个`/demo/test/get`接口。代码如下：

```java
package cn.gf.saas.module.app.controller.app;

import cn.gf.saas.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 App - Test")
@RestController
@RequestMapping("/demo/test")
@Validated
public class AppDemoTestController {

    @GetMapping("/get")
    @Operation(summary = "获取 test 信息")
    public CommonResult<String> get() {
        return success("true");
    }

}

```

在 Controller 的命名上，额外增加**App**作为前缀，一方面区分是管理后台还是用户 App 的 Controller，另一方面避免 Spring Bean
的名字冲突。

可能你会奇怪，这里我们定义了两个`/demo/test/get`接口，会不会存在重复导致冲突呢？答案，当然是并不会。原因是：

* `controller.admin`包下的接口，默认会增加`/admin-api`，即最终的访问地址是`/admin-api/demo/test/get`

* `controller.app`包下的接口，默认会增加`/app-api`，即最终的访问地址是`/app-api/demo/test/get`

## [**#**](https://cloud.iocoder.cn/module-new/#_5-%E5%90%AF%E5%8A%A8-demo-%E6%9C%8D%E5%8A%A1)5. 启动 demo 服务

① 运行 SystemServerApplication 类，将`system`服务启动。运行 InfraServerApplication 类，将`infra`服务启动。

② 运行 DemoServerApplication 类，将新建的`demo`
服务进行启动。启动完成后，使用浏览器打开[http://127.0.0.1:48099/doc.html (opens new window)](http://127.0.0.1:48099/doc.html)
地址，进入该服务的 Swagger 接口文档。

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/a457f3b9-6e37-417f-8a28-8d713a9aa092.png)

③ 打开“管理后台 - Test”接口，进行`/admin-api/demo/test/get`接口的调试，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/1f374b6a-01e6-4ad2-820e-1e365dd49b69.png)

④ 打开“用户 App - Test”接口，进行`/app-api/demo/test/get`接口的调试，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/73b51b74-4de7-49ae-acf9-5736489dd4b6.png)

## [**#**](https://cloud.iocoder.cn/module-new/#_6-%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE)6. 网关配置

① 打开`yudao-gateway`网关项目的`application.yml`配置文件，增加`demo`服务的路由配置。代码如下：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/7b72fc94-8a43-44e3-90a7-e13f4e21e0eb.png)

```yaml
        ## demo-server 服务
        - id: demo-admin-api # 路由的编号
          uri: grayLb://demo-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/admin-api/demo/**
          filters:
            - RewritePath=/admin-api/demo/v3/api-docs, /v3/api-docs # 配置，保证转发到 /v2/api-docs
        - id: demo-app-api # 路由的编号
          uri: grayLb://demo-server
          predicates: # 断言，作为路由的匹配条件，对应 RouteDefinition 数组
            - Path=/app-api/demo/**
          filters:
            - RewritePath=/app-api/demo/v3/api-docs, /v3/api-docs
```

```yaml
      - name: demo-server
        service-name: demo-server
        url: /admin-api/demo/v3/api-docs

```

② 运行 GatewayServerApplication 类，将`gateway`网关服务启动。

③ 使用浏览器打开[http://127.0.0.1:48080/doc.html (opens new window)](http://127.0.0.1:48080/doc.html)地址，进入网关的
Swagger 接口文档。然后，选择`demo-server`服务，即可进行`/admin-api/demo/test/get`和`/app-api/demo/test/get`接口的调试，如下图所示：

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/vBPlNYaj1A8XOdG8/img/bd1926ee-2345-42e3-89bf-aec972e7c8d4.png)