package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.pojo.PageResult;
import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserPageReqVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserRespVO;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.LotteryUserSaveReqVO;
import cn.gf.saas.module.activity.dal.dataobject.appuser.AppUserDO;
import cn.gf.saas.module.activity.dal.dataobject.knowledgegame.CompanyCustomActivityDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.CompanyActivityUserDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryUserDO;
import cn.gf.saas.module.activity.dal.mysql.appuser.AppUserMapper;
import cn.gf.saas.module.activity.dal.mysql.knowledgegame.CompanyCustomActivityMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.CompanyActivityUserMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryUserMapper;
import cn.gf.saas.module.activity.service.appuser.AppUserService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.LOTTERY_NOT_EXISTS;

/**
 * 幸运抽奖选择人员 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryUserServiceImpl implements LotteryUserService {

    @Resource
    private LotteryUserMapper lotteryUserMapper;

    @Resource
    private CompanyActivityUserMapper companyActivityUserMapper;

    @Resource
    private LotteryMapper lotteryMapper;

    @Resource
    private CompanyCustomActivityMapper companyCustomActivityMapper;

    @Resource
    private AppUserService appUserService;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean createLotteryUsers(LotteryUserSaveReqVO createReqVO) {
        // 校验活动是否存在
        validateLotteryExists(createReqVO.getLotteryId());

        // 查询CompanyCustomActivity的ID
        CompanyCustomActivityDO companyActivity = companyCustomActivityMapper.selectByActIdAndActType(
                String.valueOf(createReqVO.getLotteryId()), 16); // actType=16为幸运抽奖
        if (companyActivity == null) {
            throw exception(LOTTERY_NOT_EXISTS, "活动关联信息不存在");
        }

        List<LotteryUserDO> lotteryUsers = new ArrayList<>();
        List<CompanyActivityUserDO> companyActivityUsers = new ArrayList<>();

        for (String userId : createReqVO.getUserIds()) {
            // 检查lotteryuser表是否已存在
            boolean existsInLottery = lotteryUserMapper.existsByLotteryIdAndUserId(
                    createReqVO.getLotteryId(), userId);

            if (!existsInLottery) {
                // 添加到lotteryuser表
                LotteryUserDO lotteryUser = LotteryUserDO.builder()
                        .lotteryId(createReqVO.getLotteryId())
                        .userId(userId)
                        .totalCount(1) // 抽奖次数默认1
                        .usedCount(0)  // 使用次数默认0
                        .build();
                lotteryUsers.add(lotteryUser);
            }

            // 检查company_activity_user表是否已存在
            boolean existsInCompanyActivity = companyActivityUserMapper.isExistUser(
                    companyActivity.getId(), userId);

            if (!existsInCompanyActivity) {
                // 添加到company_activity_user表
                CompanyActivityUserDO companyActivityUser = CompanyActivityUserDO.builder()
                        .actId(companyActivity.getId())
                        .joinType(1) // join_type默认1
                        .joinValue(userId)
                        .build();
                companyActivityUsers.add(companyActivityUser);
            }
        }

        // 批量插入
        if (!lotteryUsers.isEmpty()) {
            lotteryUserMapper.insertBatch(lotteryUsers);
        }
        if (!companyActivityUsers.isEmpty()) {
            companyActivityUserMapper.insertBatch(companyActivityUsers);
        }

        return true;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void deleteLotteryUsers(List<String> userIds, Integer lotteryId) {
        // 校验活动是否存在
        validateLotteryExists(lotteryId);

        // 查询CompanyCustomActivity的ID
        CompanyCustomActivityDO companyActivity = companyCustomActivityMapper.selectByActIdAndActType(
                String.valueOf(lotteryId), 16); // actType=16为幸运抽奖

        // 删除lotteryuser表数据
        lotteryUserMapper.deleteByLotteryIdAndUserIds(lotteryId, userIds);

        // 删除company_activity_user表数据
        if (companyActivity != null) {
            companyActivityUserMapper.deleteByActIdAndJoinValues(
                    companyActivity.getId(), userIds);
        }
    }

    @Override
    public PageResult<LotteryUserRespVO> getLotteryUserPage(LotteryUserPageReqVO pageReqVO) {
        // 校验活动是否存在
        validateLotteryExists(pageReqVO.getLotteryId());

        // 查询lotteryuser表数据
        PageResult<LotteryUserDO> pageResult = lotteryUserMapper.selectPageByLotteryId(pageReqVO, pageReqVO.getLotteryId());

        PageResult<LotteryUserRespVO> result = BeanUtils.toBean(pageResult, LotteryUserRespVO.class);
        
        if (result.getTotal() == 0) {
            return result;
        }

        // 获取用户详细信息
        List<AppUserDO> users = appUserService.getAppUsersByIds(
                pageResult.getList().stream().map(LotteryUserDO::getUserId).collect(Collectors.toList()));

        // 设置用户信息
        for (LotteryUserRespVO item : result.getList()) {
            AppUserDO user = users.stream()
                    .filter(u -> u.getUserId().equals(item.getUserId()))
                    .findFirst()
                    .orElse(null);
            if (user != null) {
                item.setName(user.getRealName());
                item.setPhone(user.getMobilePhone());
                item.setDepartLevel(user.getDepartLevel1());
            }
        }

        return result;
    }

    /**
     * 校验抽奖活动是否存在
     */
    private void validateLotteryExists(Integer lotteryId) {
        if (lotteryMapper.selectById(lotteryId) == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }
    }
}
