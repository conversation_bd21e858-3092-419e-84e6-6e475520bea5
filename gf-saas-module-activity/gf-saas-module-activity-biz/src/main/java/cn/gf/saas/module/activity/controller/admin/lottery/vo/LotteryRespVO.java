package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 幸运抽奖 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LotteryRespVO {

    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("活动ID")
    private Integer id;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动名称")
    private String name;

    @Schema(description = "活动开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动开始时间")
    private LocalDateTime beginTime;

    @Schema(description = "活动结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动结束时间")
    private LocalDateTime endTime;

    @Schema(description = "领奖开始时间")
    @ExcelProperty("领奖开始时间")
    private LocalDateTime receiveBeginTime;

    @Schema(description = "领奖结束时间")
    @ExcelProperty("领奖结束时间")
    private LocalDateTime receiveEndTime;

    @Schema(description = "是否显示中奖金额")
    @ExcelProperty("是否显示中奖金额")
    private Boolean isShowAmount;

    @Schema(description = "抽奖模式")
    @ExcelProperty("抽奖模式")
    private Integer lotteryType;

    @Schema(description = "参与方式：0 选择用户，12 集团人员 13 公司人员")
    private Integer joinType;

    @Schema(description = "是否自定义抽奖人数")
    @ExcelProperty("是否自定义抽奖人数")
    private Boolean isLiveLottery;

    @Schema(description = "领取奖品文案")
    @ExcelProperty("领取奖品文案")
    private String receiveMsg;

    @Schema(description = "活动说明")
    @ExcelProperty("活动说明")
    private String info;

    @Schema(description = "预抽奖人数")
    @ExcelProperty("预抽奖人数")
    private Integer preUserCount;

    @Schema(description = "每人每天可抽奖")
    @ExcelProperty("每人每天可抽奖")
    private Integer lotteryOfDays;

    @Schema(description = "每人共可抽奖")
    @ExcelProperty("每人共可抽奖")
    private Integer lotteryOfTotal;

    @Schema(description = "未中奖提示")
    @ExcelProperty("未中奖提示")
    private String losingToast;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "发布时间")
    @ExcelProperty("发布时间")
    private LocalDateTime publishTime;
}
