package cn.gf.saas.module.activity.controller.admin.lottery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 奖项新增/修改 Request VO")
@Data
public class LotteryPrizeSaveReqVO {

    @Schema(description = "奖项ID", example = "1024")
    private Integer id;

    @Schema(description = "活动ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "活动ID不能为空")
    private Integer lotteryId;

    @Schema(description = "奖项名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "奖项名称不能为空")
    private String name;

    @Schema(description = "奖项类别 0虚拟券、1、实物", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "奖项类别不能为空")
    private Integer category;

    @Schema(description = "奖项图片")
    private String prizeImage;

    @Schema(description = "奖项数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "奖项数量不能为空")
    private Integer number;

    @Schema(description = "奖项金额", example = "100.00")
    private BigDecimal amount;

    @Schema(description = "面积占比", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.10")
    @NotNull(message = "面积占比不能为空")
    @DecimalMin(value = "0.00", message = "面积占比不能小于0")
    @DecimalMax(value = "1.00", message = "面积占比不能大于1")
    private BigDecimal areaRatio = BigDecimal.ZERO;

    @Schema(description = "中奖提示信息")
    private String toast;

    @Schema(description = "奖品详情")
    private String content;
}
