package cn.gf.saas.module.activity.service.lottery;

import cn.gf.saas.framework.common.util.object.BeanUtils;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.*;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryDO;
import cn.gf.saas.module.activity.dal.dataobject.lottery.LotteryPrizesDO;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryPrizesMapper;
import cn.gf.saas.module.activity.dal.mysql.lottery.LotteryWhitelistMapper;
import cn.gf.saas.module.activity.service.lottery.LotterySeedsService;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 奖项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotteryPrizeServiceImpl implements LotteryPrizeService {

    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;

    @Resource
    private LotteryMapper lotteryMapper;

    @Resource
    private LotteryWhitelistMapper lotteryWhitelistMapper;

    @Resource
    private LotterySeedsService lotterySeedsService;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Integer createPrize(LotteryPrizeSaveReqVO createReqVO) {
        // 校验活动是否存在
        validateLotteryExists(createReqVO.getLotteryId());

        // 校验活动是否已发布
        validateLotteryNotPublished(createReqVO.getLotteryId());

        // 校验奖项数量限制
        validatePrizeCountLimit(createReqVO.getLotteryId(), null);

        // 校验奖项数量不能为0
        if (createReqVO.getNumber() == null || createReqVO.getNumber() <= 0) {
            throw exception(LOTTERY_PRIZE_NUMBER_CANNOT_BE_ZERO);
        }

        // 校验奖项金额不能为0（虚拟券时）
        if (createReqVO.getCategory() != null && createReqVO.getCategory() == 0) {
            if (createReqVO.getAmount() == null || createReqVO.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw exception(LOTTERY_PRIZE_AMOUNT_CANNOT_BE_ZERO);
            }
        }

        // 当奖项类别为1实物时，奖项金额默认为1
        if (createReqVO.getCategory() != null && createReqVO.getCategory() == 1) {
            createReqVO.setAmount(BigDecimal.ONE);
        }

        // 插入奖项
        LotteryPrizesDO prize = BeanUtils.toBean(createReqVO, LotteryPrizesDO.class);
        prize.setBalance(createReqVO.getAmount()); // 余额初始为总额
        lotteryPrizesMapper.insert(prize);

        // 重新构建种子
        lotterySeedsService.restructureSeeds(createReqVO.getLotteryId(), prize.getId(), createReqVO.getNumber());

        return prize.getId();
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void updatePrize(LotteryPrizeSaveReqVO updateReqVO) {
        // 校验奖项是否存在
        validatePrizeExists(updateReqVO.getId());

        // 校验活动是否存在
        validateLotteryExists(updateReqVO.getLotteryId());

        // 校验活动是否已发布
        validateLotteryNotPublished(updateReqVO.getLotteryId());

        // 校验奖项数量限制（排除当前奖项）
        validatePrizeCountLimit(updateReqVO.getLotteryId(), updateReqVO.getId());

        // 校验白名单数量不能大于奖项数量
        Integer whitelistCount = lotteryWhitelistMapper.countByLotteryIdAndPrizeId(
                updateReqVO.getLotteryId(), updateReqVO.getId());
        if (whitelistCount > updateReqVO.getNumber()) {
            throw exception(LOTTERY_WHITELIST_EXCEED_PRIZE_COUNT);
        }

        // 更新奖项 - 手动设置所有字段确保空值也能更新
        LotteryPrizesDO updatePrize = new LotteryPrizesDO();
        updatePrize.setId(updateReqVO.getId());
        updatePrize.setLotteryId(updateReqVO.getLotteryId());
        updatePrize.setName(updateReqVO.getName());
        updatePrize.setCategory(updateReqVO.getCategory());
        updatePrize.setPrizeImage(updateReqVO.getPrizeImage()); // 允许为空
        updatePrize.setNumber(updateReqVO.getNumber());
        updatePrize.setAmount(updateReqVO.getAmount());
        updatePrize.setBalance(updateReqVO.getAmount()); // 更新余额
        updatePrize.setAreaRatio(updateReqVO.getAreaRatio());
        updatePrize.setToast(updateReqVO.getToast()); // 允许为空
        updatePrize.setContent(updateReqVO.getContent()); // 允许为空

        lotteryPrizesMapper.updateById(updatePrize);

        // 重新构建种子
        lotterySeedsService.restructureSeeds(updateReqVO.getLotteryId(), updateReqVO.getId(), updateReqVO.getNumber());
    }

    @Override
    public BigDecimal getRemainingRatio(Integer lotteryId) {
        // 校验活动是否存在
        validateLotteryExists(lotteryId);

        // 查询当前活动的所有奖项
        List<LotteryPrizesDO> prizes = lotteryPrizesMapper.selectByLotteryId(lotteryId);

        if (prizes.isEmpty()) {
            return BigDecimal.ONE; // 没有奖项时返回1.00
        }

        // 计算已使用的面积占比总和
        BigDecimal usedRatio = prizes.stream()
                .map(LotteryPrizesDO::getAreaRatio)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 剩余占比 = 1 - 已使用占比
        return BigDecimal.ONE.subtract(usedRatio);
    }

    @Override
    public List<LotteryPrizeRespVO> getPrizeList(Integer lotteryId) {
        // 校验活动是否存在
        validateLotteryExists(lotteryId);

        // 查询奖项列表
        List<LotteryPrizesDO> prizes = lotteryPrizesMapper.selectByLotteryIdOrderById(lotteryId);

        return prizes.stream().map(this::convertToPrizeRespVO).collect(Collectors.toList());
    }

    @Override
    public LotteryPrizeDetailRespVO getPrizeDetail(Integer id) {
        LotteryPrizesDO prize = lotteryPrizesMapper.selectById(id);
        if (prize == null) {
            throw exception(LOTTERY_PRIZE_NOT_EXISTS);
        }

        return BeanUtils.toBean(prize, LotteryPrizeDetailRespVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePrizes(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 校验奖项是否存在
        for (Integer id : ids) {
            validatePrizeExists(id);
        }

        // 批量删除
        lotteryPrizesMapper.deleteBatchIds(ids);
    }

    /**
     * 校验活动是否存在
     */
    private void validateLotteryExists(Integer lotteryId) {
        if (lotteryMapper.selectById(lotteryId) == null) {
            throw exception(LOTTERY_NOT_EXISTS);
        }
    }

    /**
     * 校验奖项是否存在
     */
    private void validatePrizeExists(Integer id) {
        if (lotteryPrizesMapper.selectById(id) == null) {
            throw exception(LOTTERY_PRIZE_NOT_EXISTS);
        }
    }

    /**
     * 校验奖项数量限制
     */
    private void validatePrizeCountLimit(Integer lotteryId, Integer excludeId) {
        Long count = lotteryPrizesMapper.countByLotteryIdExcludeId(lotteryId, excludeId);
        if (count >= 10) {
            throw exception(LOTTERY_PRIZE_COUNT_EXCEED_LIMIT);
        }
    }

    /**
     * 校验活动是否已发布
     */
    private void validateLotteryNotPublished(Integer lotteryId) {
        LotteryDO lottery = lotteryMapper.selectById(lotteryId);
        if (lottery != null && lottery.getPublishTime() != null) {
            throw exception(LOTTERY_ALREADY_PUBLISHED_CANNOT_MODIFY);
        }
    }

    /**
     * 转换为奖项响应VO
     */
    private LotteryPrizeRespVO convertToPrizeRespVO(LotteryPrizesDO prize) {
        LotteryPrizeRespVO respVO = BeanUtils.toBean(prize, LotteryPrizeRespVO.class);
        
        // 设置中奖金额（奖项金额）
        respVO.setWinAmount(prize.getAmount());
        
        // 根据奖项类别设置相关字段
        if (prize.getCategory() != null) {
            if (prize.getCategory() == 1) { // 实物
                respVO.setWinCount(1); // 中奖数量为1
                respVO.setTotalPrizeCount(prize.getNumber()); // 总奖项数量为奖项数量
                respVO.setTotalPrizeAmount(null); // 实物时总奖项金额为空
            } else { // 虚拟券
                respVO.setWinCount(null); // 不是实物时中奖数量为空
                respVO.setTotalPrizeCount(null); // 不是实物时总奖项数量为空
                // 总奖项金额 = 奖项数量 * 中奖金额
                if (prize.getNumber() != null && prize.getAmount() != null) {
                    respVO.setTotalPrizeAmount(
                            BigDecimal.valueOf(prize.getNumber()).multiply(prize.getAmount())
                    );
                }
            }
        }
        
        return respVO;
    }
}
