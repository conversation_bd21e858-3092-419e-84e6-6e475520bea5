package cn.gf.saas.module.activity.controller.admin.lottery;

import cn.gf.saas.framework.common.pojo.CommonResult;
import cn.gf.saas.module.activity.controller.admin.lottery.vo.*;
import cn.gf.saas.module.activity.service.lottery.LotteryPrizeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

import static cn.gf.saas.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 幸运抽奖 -奖项管理")
@RestController
@RequestMapping("/activity/lottery/prize")
@Validated
public class LotteryPrizeController {

    @Resource
    private LotteryPrizeService lotteryPrizeService;

    @PostMapping("/create")
    @Operation(summary = "创建奖项")
    public CommonResult<Integer> createPrize(@Valid @RequestBody LotteryPrizeSaveReqVO createReqVO) {
        return success(lotteryPrizeService.createPrize(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新奖项")
    public CommonResult<Boolean> updatePrize(@Valid @RequestBody LotteryPrizeSaveReqVO updateReqVO) {
        lotteryPrizeService.updatePrize(updateReqVO);
        return success(true);
    }

    @GetMapping("/remaining-ratio")
    @Operation(summary = "获得剩余占比")
    @Parameter(name = "lotteryId", description = "活动ID", required = true, example = "1024")
    public CommonResult<LotteryRemainingRatioRespVO> getRemainingRatio(@RequestParam("lotteryId") Integer lotteryId) {
        BigDecimal remainingRatio = lotteryPrizeService.getRemainingRatio(lotteryId);
        LotteryRemainingRatioRespVO respVO = new LotteryRemainingRatioRespVO();
        respVO.setRemainingRatio(remainingRatio);
        return success(respVO);
    }

    @GetMapping("/list")
    @Operation(summary = "获得奖项列表")
    @Parameter(name = "lotteryId", description = "活动ID", required = true, example = "1024")
    public CommonResult<List<LotteryPrizeRespVO>> getPrizeList(@RequestParam("lotteryId") Integer lotteryId) {
        List<LotteryPrizeRespVO> list = lotteryPrizeService.getPrizeList(lotteryId);
        return success(list);
    }

    @GetMapping("/get")
    @Operation(summary = "获得奖项详情")
    @Parameter(name = "id", description = "奖项ID", required = true, example = "1024")
    public CommonResult<LotteryPrizeDetailRespVO> getPrizeDetail(@RequestParam("id") Integer id) {
        LotteryPrizeDetailRespVO respVO = lotteryPrizeService.getPrizeDetail(id);
        return success(respVO);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "批量删除奖项")
    @Parameter(name = "ids", description = "奖项IDS", required = true, example = "逗号分隔：1,2,3")
    public CommonResult<Boolean> deletePrizes(@RequestParam("ids") List<Integer> ids) {
        lotteryPrizeService.deletePrizes(ids);
        return success(true);
    }
}
