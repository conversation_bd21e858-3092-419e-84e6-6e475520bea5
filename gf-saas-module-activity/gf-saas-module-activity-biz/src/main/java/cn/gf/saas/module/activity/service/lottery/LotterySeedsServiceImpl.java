package cn.gf.saas.module.activity.service.lottery;
import cn.gf.saas.module.activity.dal.dataobject.lottery.*;
import cn.gf.saas.module.activity.dal.mysql.lottery.*;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.gf.saas.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.gf.saas.module.activity.enums.ErrorCodeConstants.*;

/**
 * 抽奖种子 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class LotterySeedsServiceImpl implements LotterySeedsService {

    @Resource
    private LotterySeedsMapper lotterySeedsMapper;

    @Resource
    private LotteryUserMapper lotteryUserMapper;

    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;

    @Resource
    private LotteryWhitelistMapper lotteryWhitelistMapper;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean resetSeeds(Integer lotteryId) {
        // 检查参与人员
        Long userCount = lotteryUserMapper.countByLotteryId(lotteryId);
        if (userCount <= 0) {
            throw exception(LOTTERY_NO_USERS);
        }

        // 检查种子数据
        List<LotterySeedsDO> seedsList = lotterySeedsMapper.selectByLotteryId(lotteryId);
        if (seedsList.isEmpty()) {
            throw exception(LOTTERY_NO_PRIZES);
        }

        // 查询白名单
        List<LotteryWhitelistDO> whitelist = lotteryWhitelistMapper.selectByLotteryId(lotteryId);

        if (!whitelist.isEmpty()) {
            // 白名单存在，走另外一套生成种子
            resetSeedsWithWhitelist(lotteryId, seedsList, whitelist, userCount.intValue());
        } else {
            // 无白名单，随机生成种子
            resetSeedsWithoutWhitelist(seedsList, userCount.intValue());
        }

        return true;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public Boolean restructureSeeds(Integer lotteryId, Integer prizesId, Integer count) {
        // 清除历史种子
        lotterySeedsMapper.deleteByLotteryIdAndPrizesId(lotteryId, prizesId);

        // 创建新种子
        List<LotterySeedsDO> newSeeds = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            LotterySeedsDO seed = LotterySeedsDO.builder()
                    .lotteryId(lotteryId)
                    .prizesId(prizesId)
                    .seedNo(-(i + 1)) // 初始化种子为负数
                    .build();
            newSeeds.add(seed);
        }

        return lotterySeedsMapper.insertBatch(newSeeds);
    }

    /**
     * 有白名单的种子重置
     */
    private void resetSeedsWithWhitelist(Integer lotteryId, List<LotterySeedsDO> seedsList, 
                                       List<LotteryWhitelistDO> whitelist, Integer userCount) {
        // 获取所有奖项
        List<LotteryPrizesDO> allPrizes = lotteryPrizesMapper.selectByLotteryId(lotteryId);

        int j = 0;
        int k = 0; // 确保不取重复数
        int count = seedsList.size() - whitelist.size();
        int rangeCount = userCount - whitelist.size();
        
        // 生成随机数组
        List<Integer> rdmArray = new ArrayList<>();
        for (int i = 1; i <= Math.max(count, rangeCount); i++) {
            rdmArray.add(i);
        }
        Collections.shuffle(rdmArray);
        
        List<Integer> rdmIndex = new ArrayList<>();
        for (int i = 0; i < rdmArray.size(); i++) {
            rdmIndex.add(i);
        }
        Collections.shuffle(rdmIndex);

        List<LotterySeedsDO> seedsToUpdate = new ArrayList<>();

        for (LotteryPrizesDO prize : allPrizes) {
            // 获取该奖项的种子
            List<LotterySeedsDO> prizeSeeds = seedsList.stream()
                    .filter(s -> s.getPrizesId().equals(prize.getId()))
                    .collect(Collectors.toList());

            // 获取该奖项的白名单
            List<LotteryWhitelistDO> whitePrize = whitelist.stream()
                    .filter(w -> w.getPrizeId().equals(prize.getId()))
                    .collect(Collectors.toList());

            // 白名单递增
            for (int i = 0; i < whitePrize.size() && i < prizeSeeds.size(); i++) {
                prizeSeeds.get(i).setSeedNo(j + 1);
                seedsToUpdate.add(prizeSeeds.get(i));
                j++;
            }

            // 非白名单随机
            for (int i = whitePrize.size(); i < prizeSeeds.size(); i++) {
                if (k < rdmIndex.size()) {
                    prizeSeeds.get(i).setSeedNo(whitelist.size() + rdmArray.get(rdmIndex.get(k)));
                    seedsToUpdate.add(prizeSeeds.get(i));
                    k++;
                }
            }
        }

        // 批量更新
        for (LotterySeedsDO seed : seedsToUpdate) {
            lotterySeedsMapper.updateById(seed);
        }
    }

    /**
     * 无白名单的种子重置
     */
    private void resetSeedsWithoutWhitelist(List<LotterySeedsDO> seedsList, Integer userCount) {
        // 生成随机数组
        List<Integer> rdmArray = new ArrayList<>();
        for (int i = 1; i <= Math.max(seedsList.size(), userCount); i++) {
            rdmArray.add(i);
        }
        Collections.shuffle(rdmArray);
        
        List<Integer> rdmIndex = new ArrayList<>();
        for (int i = 0; i < Math.max(seedsList.size(), userCount); i++) {
            rdmIndex.add(i);
        }
        Collections.shuffle(rdmIndex);

        // 从已生成的数组中随机选取种子
        for (int i = 0; i < seedsList.size(); i++) {
            if (i < rdmIndex.size()) {
                seedsList.get(i).setSeedNo(rdmArray.get(rdmIndex.get(i)));
                lotterySeedsMapper.updateById(seedsList.get(i));
            }
        }
    }
}
