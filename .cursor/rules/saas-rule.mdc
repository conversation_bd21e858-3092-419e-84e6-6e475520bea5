---
description: 这是一个saas的规则
globs: 
alwaysApply: false
---
1. 技术栈与基础规范
统一使用 Java 11，Spring Boot 2.x 版本。
ORM 框架采用 MyBatis-Plus，接口文档采用 Swagger。
Redis 作为缓存组件，需合理使用。
所有业务均需支持多租户（tenant-id），并实现租户隔离。
所有接口均需实现 Authorization 鉴权。
2. 代码风格与注释
严格遵循项目既有代码风格，命名规范统一（类名大驼峰、变量小驼峰、常量全大写下划线）。
代码必须添加规范的注释，包括类注释、方法注释、参数说明、返回值说明、异常说明。
注释风格统一采用 Javadoc 格式，示例：
/**
   * 新增职工学院信息
   * @param reqVO 新增请求参数
   * @return 新增记录主键
   */
3. 模块与目录结构
业务代码按模块分包，controller、service、serviceImpl、mapper、convert、vo、entity（do）等分层清晰。
新增业务表，需同步生成 Entity/DO、VO、Mapper、Service、ServiceImpl、Convert、Controller 等分层代码。
VO 层参数与数据库实体解耦，非接口参数字段仅实体映射。
4. 参数校验与异常处理
所有接口参数需使用 Spring Validation 进行校验，必要时自定义注解。
业务异常需归属专属异常码，统一在 ErrorCodeConstants 中维护，避免复用其他模块异常码。
统一异常处理，返回标准错误响应结构。
5. 多租户与鉴权
所有数据操作必须带 tenant-id，确保租户隔离。
所有接口需校验 Authorization，未授权请求直接拒绝。
6. JSON/特殊字段处理
复杂字段如 banners（List<String>）等，接口参数为 List，数据库存储为 JSON 字符串，接口返回时需转换。
时间字段需校验逻辑合理性（如开始时间小于结束时间），校验失败抛出专属异常码。
7. Git 操作与忽略规则
所有开发需先同步 git，未初始化需先 git init。
代码变更需及时 git add/commit，提交信息规范简明。
.gitignore 必须包含 target/、/.flattened-pom.xml、.DS_Store、.idea/、.vscode/、node_modules/ 等无关文件。
禁止将 IDE 配置、编译输出、临时文件、flattened-pom.xml 等加入版本库。
发现误提交需用 git rm --cached 移除并重新提交。
8. 开发日志与测试清单
每次开发需维护开发日志，首行写 UTC 时间戳，简述本次开发内容。
重要功能开发后需补充测试清单，列明测试点、预期结果。
9. 其他
代码合并前需自测通过，确保无明显 bug。
发现规范遗漏或需补充，及时沟通并完善本规则。